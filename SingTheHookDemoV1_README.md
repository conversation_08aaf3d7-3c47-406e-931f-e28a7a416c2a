## 一、项目技术文档（v1.0 Demo）

1. 目标
   用 Swift 5.9 + SwiftUI + Combine 在 iOS 16+ 上做一个**最小可运行的本地音乐播放器 Demo**，功能只有：
   • 播放本地 mp3文件
   • 按时间戳同步滚动并高亮歌词
   • 预留「Record」入口（暂不实现）

2. 技术栈
   • 语言：Swift 5.9
   • UI：SwiftUI + ScrollViewReader 实现歌词滚动
   • 逻辑：Combine（Timer.publish）驱动歌词刷新
   • 音频：AVPlayer
   • 资源：本地 Bundle 中的 `test_1.src 和 test_1.mp3`
   • 包管理：0 第三方，纯 SPM

3. 目录结构

   ```
   KaraokeDemo/
   ├── App/
   │   └── KaraokeDemoApp.swift
   ├── Models/
   │   ├── LyricLine.swift
   │   └── SRTParser.swift
   ├── ViewModels/
   │   └── KaraokeEngine.swift
   ├── Views/
   │   ├── ContentView.swift
   │   └── LyricRow.swift
   └── Resources/
       ├── test_1.mp3
       └── test_1.srt
   ```
   
   
   
4. 数据流

   ```
   test_1.srt → SRTParser → [LyricLine]
   AVPlayer.currentTime → KaraokeEngine.currentTime → ContentView → LyricRow
   ```
   
   
   
5. 已知限制
   • 仅支持 iOS 16+（ScrollViewReader / LazyVStack）
   • 无缓存、无网络、无录音
   • Demo 级错误处理（几乎为 0）
