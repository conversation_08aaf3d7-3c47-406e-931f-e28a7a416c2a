//
//  LyricLine.swift
//  SingTheHookDemoV1
//
//  Created by shin on 2025/8/10.
//

import Foundation

struct LyricLine: Identifiable, Hashable {
    let id = UUID()
    let range: ClosedRange<TimeInterval>
    let fullText: String
    let tokens: [LyricToken]?
    
    // 调试信息
    var debugDescription: String {
        var desc = "LyricLine: \"\(fullText)\" [\(range.lowerBound)-\(range.upperBound)]"
        if let tokens = tokens {
            desc += " 词块数: \(tokens.count)"
            for (i, token) in tokens.enumerated() {
                desc += "\n  - Token \(i): \"\(token.text)\" @ \(token.time)"
            }
        } else {
            desc += " (无词块)"
        }
        return desc
    }
}


/// 行内每个时间戳片段
struct LyricToken: Hashable {
    let text: String
    let time: TimeInterval
}
