//
//  SRTParser.swift
//  SingTheHookDemoV1
//
//  Created by shin on 2025/8/10.
//
import Foundation

enum SRTParser {
    /// 返回 [LyricLine]
    static func parse(_ srt: String) -> [LyricLine] {
        // 1. 统一换行符
        let clean = srt.replacingOccurrences(of: "\r\n", with: "\n")
                      .replacingOccurrences(of: "\r",   with: "\n")

        // 2. 用两个换行把字幕块切开
        let blocks = clean.components(separatedBy: "\n\n")
                          .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
                          .filter { !$0.isEmpty }

        return blocks.compactMap(parse(block:))
    }

    // MARK: - 单条字幕块 -> LyricLine
    private static func parse(block: String) -> LyricLine? {
        let lines = block
            .split(omittingEmptySubsequences: false, whereSeparator: \.isNewline)
            .map(String.init)

        guard lines.count >= 3 else { return nil }

        let timeLine = lines[1]          // 00:00:02,180 --> 00:00:04,540
        let textLines = lines.dropFirst(2).joined(separator: "\n")

        let timePattern = #"(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})"#
        guard let timeRegex = try? NSRegularExpression(pattern: timePattern),
              let match = timeRegex.firstMatch(in: timeLine, range: NSRange(timeLine.startIndex..., in: timeLine)),
              match.numberOfRanges == 3 else { return nil }

        let startStr = String(timeLine[Range(match.range(at: 1), in: timeLine)!])
        let endStr   = String(timeLine[Range(match.range(at: 2), in: timeLine)!])

        let start = parse(timeString: startStr)
        let end   = parse(timeString: endStr)

        // 解析tokens，确保正确处理空格
        let tokens = parseTokens(from: textLines)
        
        // 从tokens重建fullText，保留空格
        var fullText = ""
        if let tokens = tokens {
            fullText = tokens.map { $0.text }.joined(separator: " ")
        } else {
            // 如果没有tokens，则使用原始文本去除时间标记
            fullText = textLines
                .replacingOccurrences(of: #"<[^>]+>"#, with: "", options: .regularExpression)
                .trimmingCharacters(in: .whitespacesAndNewlines)
        }

        return LyricLine(range: start...end,
                         fullText: fullText,
                         tokens: tokens)
    }

    // MARK: - 解析 HH:MM:SS,mmm -> seconds
    private static func parse(timeString: String) -> TimeInterval {
        let parts = timeString.components(separatedBy: CharacterSet(charactersIn: ":,"))
        guard parts.count == 4 else { return 0 }
        return Double(parts[0])! * 3600
             + Double(parts[1])! * 60
             + Double(parts[2])!
             + Double(parts[3])! / 1000
    }

    // MARK: - 解析行内 <00:03.03>WORD 片段
    private static func parseTokens(from text: String) -> [LyricToken]? {
        // 匹配 <mm:ss.cent>WORD
        let pattern = #"<(\d{2}):(\d{2}\.\d{2})>([^<]*)"#
        guard let regex = try? NSRegularExpression(pattern: pattern) else { return nil }

        let matches = regex.matches(in: text, range: NSRange(text.startIndex..., in: text))
        guard !matches.isEmpty else { return nil }

        return matches.map {
            let minStr  = String(text[Range($0.range(at: 1), in: text)!]) // 分钟
            let secStr  = String(text[Range($0.range(at: 2), in: text)!]) // 秒.毫秒
            let word    = String(text[Range($0.range(at: 3), in: text)!]).trimmingCharacters(in: .whitespaces)  // Trim whitespace

            let minutes = Double(minStr)!
            let seconds = Double(secStr)!
            let total   = minutes * 60 + seconds

            return LyricToken(text: word, time: total)
        }
    }
}
