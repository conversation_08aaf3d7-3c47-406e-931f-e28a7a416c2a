//
//  KaraokeEngine.swift
//  SingTheHookDemoV1
//
//  Created by shin on 2025/8/10.
//

import Foundation
//import MediaPlayer
import AVFoundation
import Combine

@MainActor
final class KaraokeEngine: ObservableObject {
    @Published var currentTime: TimeInterval = 0
    @Published var isPlaying = false
    @Published var lyrics: [LyricLine] = []
    @Published var duration: TimeInterval = 0

    private let player = AVPlayer()
    private var timer: AnyCancellable?
    private var playerObserver: Any?

    init() {
        guard let url = Bundle.main.url(forResource: "test_1", withExtension: "mp3") else {
            print("❌ 音频文件未找到")
            return
        }
        let playerItem = AVPlayerItem(url: url)
        player.replaceCurrentItem(with: playerItem)
        
        // Add observer for item duration
        playerObserver = player.currentItem?.observe(\.duration, options: [.new]) { [weak self] item, _ in
            self?.duration = item.duration.seconds
        }

        guard let srtURL = Bundle.main.url(forResource: "test_1", withExtension: "srt"),
              let srt = try? String(contentsOf: srtURL) else {
            print("❌ SRT 文件未找到或无法读取")
            return
        }
        lyrics = SRTParser.parse(srt)
        print("✅ SRT 解析完成：共 \(lyrics.count) 行")
    }
    
    deinit {
        if let playerObserver = playerObserver {
            NotificationCenter.default.removeObserver(playerObserver)
        }
        timer?.cancel()
    }

    func play() {
        player.play()
        isPlaying = true
        
        // 调试信息：打印所有歌词行的详细信息
        print("\n===== 歌词调试信息 =====")
        for (index, line) in lyrics.enumerated() {
            print("[\(index)] \(line.debugDescription)")
        }
        print("=======================\n")
        
        // 使用更高频率的计时器来确保歌词同步更加准确
        timer?.cancel()
        timer = Timer.publish(every: 0.02, on: .main, in: .common) // 提高到50Hz
            .autoconnect()
            .sink { [weak self] _ in
                guard let self = self else { return }
                let newTime = self.player.currentTime().seconds
                if !newTime.isNaN && newTime > 0 {
                    // 强制在主线程更新UI
                    DispatchQueue.main.async {
                        self.currentTime = newTime
                        
                        // 调试：查找当前活跃行
                        if let activeLine = self.lyrics.first(where: { $0.range.contains(newTime) }) {
                            // 只在行变化时打印，避免过多日志
                            if self.currentActiveLine?.id != activeLine.id {
                                print("当前时间: \(String(format: "%.2f", newTime)), 匹配行: \(activeLine.fullText)")
                                self.currentActiveLine = activeLine
                            }
                        }
                    }
                }
            }
    }

    func pause() {
        player.pause()
        isPlaying = false
        timer?.cancel()
    }

    func restart() {
        player.seek(to: .zero)
        currentTime = 0
        play()
    }
    
    // 跟踪当前活跃行，避免重复打印
    private var currentActiveLine: LyricLine?
}
