//
//  LyricRow.swift
//  SingTheHookDemoV1
//
//  Created by shin on 2025/8/10.
//

import SwiftUI

struct LyricRow: View, Hashable {
    let line: LyricLine
    let current: TimeInterval
    
    private var isActive: Bool {
        line.range.contains(current)
    }
    
    var body: some View {
        ZStack {
            // 背景层 - 当前行高亮
            if isActive {
                RoundedRectangle(cornerRadius: 12)
                    .fill(LinearGradient(
                        gradient: Gradient(colors: [Color.purple.opacity(0.7), Color.blue.opacity(0.5)]),
                        startPoint: .leading,
                        endPoint: .trailing
                    ))
                    .shadow(color: Color.purple.opacity(0.5), radius: 5)
            }
            
            // 内容层
            VStack(spacing: 4) {
                // 主要歌词内容
                if let tokens = line.tokens {
                    HStack(spacing: 8) {
                        ForEach(Array(tokens.enumerated()), id: \.offset) { index, token in
                            // 只显示有内容的token
                            if !token.text.isEmpty {
                                Text(token.text)
                                    .font(.title2.weight(current >= token.time ? .bold : .regular))
                                    .foregroundColor(current >= token.time ? .yellow : (isActive ? .white : .gray))
                                    .padding(4)
                                    .background(current >= token.time ? Color.orange.opacity(0.3) : Color.clear)
                                    .cornerRadius(4)
                            }
                        }
                    }
                } else {
                    Text(line.fullText)
                        .font(.title2)
                        .foregroundColor(isActive ? .white : .gray)
                }
                
                // 调试信息 - 显示时间范围
                if isActive {
                    Text("[\(String(format: "%.2f", line.range.lowerBound))-\(String(format: "%.2f", line.range.upperBound))]")
                        .font(.caption2)
                        .foregroundColor(.gray)
                }
            }
            .padding(isActive ? 12 : 8)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 2)
        .scaleEffect(isActive ? 1.05 : 1.0)
        .animation(.spring(response: 0.3), value: isActive)
    }
    
    // Hashable
    static func == (lhs: LyricRow, rhs: LyricRow) -> Bool {
        lhs.line.id == rhs.line.id
    }
    func hash(into hasher: inout Hasher) {
        hasher.combine(line.id)
    }
}

#Preview {
    let sample = LyricLine(
        range: 3.0...5.0,
        fullText: "Hello SwiftUI",
        tokens: [
            LyricToken(text: "Hello", time: 3.0),
            LyricToken(text: "SwiftUI", time: 4.0)
        ]
    )
    return VStack {
        LyricRow(line: sample, current: 3.5)
        LyricRow(line: sample, current: 5.5)
    }
    .preferredColorScheme(.dark)
}
