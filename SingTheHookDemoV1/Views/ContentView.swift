//
//  ContentView.swift
//  SingTheHookDemoV1
//
//  Created by shin on 2025/8/10.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var engine = KaraokeEngine()
    @State private var showDebug = false
    @State private var activeLineText = ""
    @State private var currentActiveLineId: UUID?
    @State private var refreshID = UUID() // 用于强制刷新视图
    @State private var uiRefreshTimer: Timer? = nil // 定时刷新UI

    var body: some View {
        VStack(spacing: 30) {
            Text("Vocal Studio")
                .font(.largeTitle.bold())
                
            // 调试信息
            if showDebug {
                VStack(alignment: .leading, spacing: 5) {
                    Text("当前时间: \(String(format: "%.2f", engine.currentTime))秒")
                    Text("当前行: \(activeLineText)")
                    
                    if let activeLine = engine.lyrics.first(where: { $0.range.contains(engine.currentTime) }) {
                        Text("行ID: \(activeLine.id)")
                        Text("时间范围: [\(String(format: "%.2f", activeLine.range.lowerBound))-\(String(format: "%.2f", activeLine.range.upperBound))]")
                        
                        if let tokens = activeLine.tokens {
                            Text("词块数量: \(tokens.count)")
                            ForEach(tokens.indices, id: \.self) { i in
                                let token = tokens[i]
                                let isActive = engine.currentTime >= token.time
                                Text("词块\(i): '\(token.text)' @ \(String(format: "%.2f", token.time))秒 \(isActive ? "✓" : "")")
                                    .foregroundColor(isActive ? .green : .gray)
                            }
                        }
                    }
                }
                .font(.caption)
                .padding(8)
                .background(Color.black.opacity(0.1))
                .cornerRadius(5)
            }

            // 歌词区
            ScrollViewReader { proxy in
                ScrollView {
                    if engine.lyrics.isEmpty {
                        Text("歌词加载失败或未找到")
                            .foregroundColor(.secondary)
                    } else {
                        LazyVStack(spacing: 16) {
                            // 顶部空间
                            Spacer().frame(height: 180)
                            
                            ForEach(engine.lyrics, id: \.id) { line in
                                LyricRow(line: line, current: engine.currentTime)
                                    .id(line.id)
                                    .padding(.horizontal, 8)
                            }
                            
                            // 底部空间
                            Spacer().frame(height: 180)
                        }
                        .id(refreshID) // 用于强制刷新
                    }
                }
                .frame(maxWidth: .infinity)
                .onChange(of: engine.currentTime) { time in
                    // 查找当前应该高亮的行
                    if let activeLine = engine.lyrics.first(where: { $0.range.contains(time) }) {
                        activeLineText = activeLine.fullText
                        
                        // 只有当行ID变化时才滚动，避免频繁滚动
                        if currentActiveLineId != activeLine.id {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                proxy.scrollTo(activeLine.id, anchor: .center)
                                currentActiveLineId = activeLine.id
                                
                                // 强制刷新视图以确保高亮状态更新
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                    self.refreshID = UUID()
                                }
                            }
                        }
                    }
                }
            }
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(white: 0.08))
                    .shadow(color: Color.black.opacity(0.3), radius: 10)
            )

            // 控制区
            VStack {
                // Progress bar
                if engine.duration > 0 {
                    HStack {
                        Text(formatTime(engine.currentTime))
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        ProgressView(value: engine.currentTime, total: engine.duration)
                            .progressViewStyle(LinearProgressViewStyle())
                        
                        Text(formatTime(engine.duration))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.horizontal)
                }
                
                HStack(spacing: 20) {
                    Button(engine.isPlaying ? "Pause" : "Play") {
                        engine.isPlaying ? engine.pause() : engine.play()
                    }
                    .buttonStyle(.borderedProminent)
                    
                    Button("Restart") {
                        engine.restart()
                        // 强制刷新视图
                        refreshID = UUID()
                    }
                    .buttonStyle(.bordered)
                    
                    Button(showDebug ? "隐藏调试" : "显示调试") {
                        showDebug.toggle()
                    }
                    .buttonStyle(.bordered)
                }
            }
        }
        .padding()
        .onAppear {
            // 启动定时刷新机制，每0.5秒刷新一次UI
            uiRefreshTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { _ in
                refreshID = UUID()
            }
        }
        .onDisappear {
            uiRefreshTimer?.invalidate()
            uiRefreshTimer = nil
        }
    }
    
    private func formatTime(_ timeInterval: TimeInterval) -> String {
        let minutes = Int(timeInterval) / 60
        let seconds = Int(timeInterval) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
}

#Preview {
    ContentView()
}
